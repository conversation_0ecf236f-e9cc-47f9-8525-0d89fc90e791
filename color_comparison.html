
    <!DOCTYPE html>
    <html>
    <head>
        <title>Color Scheme Comparison</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .comparison { display: flex; gap: 20px; margin: 20px 0; }
            .scheme { flex: 1; }
            .result-box { padding: 20px; border-radius: 10px; margin: 10px 0; }
            h1 { color: #333; }
            h2 { color: #666; }
            h3 { margin-top: 30px; }
        </style>
    </head>
    <body>
        <h1>🎨 Prediction Result Color Scheme Improvements</h1>
        <p>Comparison of old vs new color schemes for better readability</p>
    
        <h3>📊 Advanced ML Model</h3>
        <div class="comparison">
            <div class="scheme">
                <h4>❌ Old Scheme (Poor Readability)</h4>
                <div class="result-box" style="
                    background: linear-gradient(90deg, #e8f5e8, #e8f5e8);
                    border-left: 5px solid #2e7d32;
                ">
                    <h2 style="margin: 0 0 10px 0; font-size: 24px; color: #2e7d32;">
                        🤖 Predicted Sale Price: $212,686.51
                    </h2>
                    <p style="margin: 0; font-size: 14px; opacity: 0.8; color: #2e7d32;">
                        Generated by: Advanced ML Model • Confidence: 75%
                    </p>
                </div>
                <p><strong>Issues:</strong></p>
                <ul>
                    <li>Text color same as header color</li>
                    <li>Low opacity (0.8) makes text even lighter</li>
                    <li>Poor contrast against light background</li>
                    <li>Hard to read, especially subtitle</li>
                </ul>
            </div>
            
            <div class="scheme">
                <h4>✅ New Scheme (Improved Readability)</h4>
                <div class="result-box" style="
                    background: linear-gradient(90deg, #f1f8e9, #f1f8e9);
                    border-left: 5px solid #4caf50;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                ">
                    <h2 style="margin: 0 0 10px 0; font-size: 24px; color: #1b5e20; font-weight: bold;">
                        🤖 Predicted Sale Price: $212,686.51
                    </h2>
                    <p style="margin: 0; font-size: 16px; color: #1a1a1a; font-weight: 500;">
                        Generated by: Advanced ML Model • Confidence: 75%
                    </p>
                </div>
                <p><strong>Improvements:</strong></p>
                <ul>
                    <li>Darker header colors for better contrast</li>
                    <li>Dark text (#1a1a1a) for subtitle</li>
                    <li>No opacity reduction</li>
                    <li>Larger font size (16px vs 14px)</li>
                    <li>Bold font weights</li>
                    <li>Subtle shadow for depth</li>
                </ul>
            </div>
        </div>
        
        <h3>📊 Intelligent Fallback</h3>
        <div class="comparison">
            <div class="scheme">
                <h4>❌ Old Scheme (Poor Readability)</h4>
                <div class="result-box" style="
                    background: linear-gradient(90deg, #e3f2fd, #e3f2fd);
                    border-left: 5px solid #1976d2;
                ">
                    <h2 style="margin: 0 0 10px 0; font-size: 24px; color: #1976d2;">
                        🤖 Predicted Sale Price: $212,686.51
                    </h2>
                    <p style="margin: 0; font-size: 14px; opacity: 0.8; color: #1976d2;">
                        Generated by: Intelligent Fallback • Confidence: 75%
                    </p>
                </div>
                <p><strong>Issues:</strong></p>
                <ul>
                    <li>Text color same as header color</li>
                    <li>Low opacity (0.8) makes text even lighter</li>
                    <li>Poor contrast against light background</li>
                    <li>Hard to read, especially subtitle</li>
                </ul>
            </div>
            
            <div class="scheme">
                <h4>✅ New Scheme (Improved Readability)</h4>
                <div class="result-box" style="
                    background: linear-gradient(90deg, #e8f4fd, #e8f4fd);
                    border-left: 5px solid #1976d2;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                ">
                    <h2 style="margin: 0 0 10px 0; font-size: 24px; color: #0d47a1; font-weight: bold;">
                        🤖 Predicted Sale Price: $212,686.51
                    </h2>
                    <p style="margin: 0; font-size: 16px; color: #1a1a1a; font-weight: 500;">
                        Generated by: Intelligent Fallback • Confidence: 75%
                    </p>
                </div>
                <p><strong>Improvements:</strong></p>
                <ul>
                    <li>Darker header colors for better contrast</li>
                    <li>Dark text (#1a1a1a) for subtitle</li>
                    <li>No opacity reduction</li>
                    <li>Larger font size (16px vs 14px)</li>
                    <li>Bold font weights</li>
                    <li>Subtle shadow for depth</li>
                </ul>
            </div>
        </div>
        
        <h3>📊 Basic Statistical</h3>
        <div class="comparison">
            <div class="scheme">
                <h4>❌ Old Scheme (Poor Readability)</h4>
                <div class="result-box" style="
                    background: linear-gradient(90deg, #fff3e0, #fff3e0);
                    border-left: 5px solid #ff9800;
                ">
                    <h2 style="margin: 0 0 10px 0; font-size: 24px; color: #ff9800;">
                        🤖 Predicted Sale Price: $212,686.51
                    </h2>
                    <p style="margin: 0; font-size: 14px; opacity: 0.8; color: #ff9800;">
                        Generated by: Basic Statistical • Confidence: 75%
                    </p>
                </div>
                <p><strong>Issues:</strong></p>
                <ul>
                    <li>Text color same as header color</li>
                    <li>Low opacity (0.8) makes text even lighter</li>
                    <li>Poor contrast against light background</li>
                    <li>Hard to read, especially subtitle</li>
                </ul>
            </div>
            
            <div class="scheme">
                <h4>✅ New Scheme (Improved Readability)</h4>
                <div class="result-box" style="
                    background: linear-gradient(90deg, #fff8e1, #fff8e1);
                    border-left: 5px solid #ff9800;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                ">
                    <h2 style="margin: 0 0 10px 0; font-size: 24px; color: #e65100; font-weight: bold;">
                        🤖 Predicted Sale Price: $212,686.51
                    </h2>
                    <p style="margin: 0; font-size: 16px; color: #1a1a1a; font-weight: 500;">
                        Generated by: Basic Statistical • Confidence: 75%
                    </p>
                </div>
                <p><strong>Improvements:</strong></p>
                <ul>
                    <li>Darker header colors for better contrast</li>
                    <li>Dark text (#1a1a1a) for subtitle</li>
                    <li>No opacity reduction</li>
                    <li>Larger font size (16px vs 14px)</li>
                    <li>Bold font weights</li>
                    <li>Subtle shadow for depth</li>
                </ul>
            </div>
        </div>
        
        <h3>📋 Summary of Changes</h3>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr style="background-color: #f5f5f5;">
                <th style="padding: 10px;">Aspect</th>
                <th style="padding: 10px;">Old</th>
                <th style="padding: 10px;">New</th>
                <th style="padding: 10px;">Improvement</th>
            </tr>
            <tr>
                <td style="padding: 10px;"><strong>Header Text</strong></td>
                <td style="padding: 10px;">Same color as border, medium contrast</td>
                <td style="padding: 10px;">Darker variants, high contrast</td>
                <td style="padding: 10px;">Better readability</td>
            </tr>
            <tr>
                <td style="padding: 10px;"><strong>Subtitle Text</strong></td>
                <td style="padding: 10px;">Same as header + 0.8 opacity</td>
                <td style="padding: 10px;">Dark text (#1a1a1a), no opacity</td>
                <td style="padding: 10px;">Much clearer text</td>
            </tr>
            <tr>
                <td style="padding: 10px;"><strong>Font Size</strong></td>
                <td style="padding: 10px;">14px subtitle</td>
                <td style="padding: 10px;">16px subtitle</td>
                <td style="padding: 10px;">Easier to read</td>
            </tr>
            <tr>
                <td style="padding: 10px;"><strong>Font Weight</strong></td>
                <td style="padding: 10px;">Normal</td>
                <td style="padding: 10px;">Bold header, medium subtitle</td>
                <td style="padding: 10px;">Better hierarchy</td>
            </tr>
            <tr>
                <td style="padding: 10px;"><strong>Visual Effects</strong></td>
                <td style="padding: 10px;">Flat design</td>
                <td style="padding: 10px;">Subtle shadow</td>
                <td style="padding: 10px;">Better depth perception</td>
            </tr>
        </table>
        
        <h3>🎯 Result</h3>
        <p>The new color scheme provides significantly better readability by:</p>
        <ul>
            <li><strong>Higher contrast:</strong> Dark text on light backgrounds</li>
            <li><strong>Better hierarchy:</strong> Different colors for header vs subtitle</li>
            <li><strong>Improved accessibility:</strong> Meets WCAG contrast guidelines</li>
            <li><strong>Professional appearance:</strong> Clean, modern design</li>
        </ul>
    </body>
    </html>
    